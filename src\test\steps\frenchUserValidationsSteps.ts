import { Given, When, Then } from "@cucumber/cucumber";
import { fixture } from "../../hooks/pageFixture";
import FrenchUserPage from "../../pages/frenchUserPage";
let frenchuserpage: FrenchUserPage;


Then('User navigate to each page and verify all content is in french language', async function () {
    // Write code here that turns the phrase above into concrete actions
    frenchuserpage = new FrenchUserPage(fixture.page);
    await frenchuserpage.CheckAllPagesAreInFrench();
});