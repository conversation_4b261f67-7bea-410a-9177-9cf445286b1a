{"cookies": [{"name": "_csrf", "value": "e_vkm-fnq3uIpwfFOhQjjph6", "domain": "cpsdev.us.auth0.com", "path": "/usernamepassword/login", "expires": 1758902198.6971, "httpOnly": true, "secure": true, "sameSite": "Lax"}, {"name": "ai_user", "value": "8BYww0nxWjx1TNcklA9WLZ|2025-09-15T13:55:14.068Z", "domain": "sqa-app.ecps.ca", "path": "/", "expires": 1789480514.069012, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "did", "value": "s%3Av0%3A2ef0e4ff-7110-42ec-9e33-2c691c7dee3e.fYlXKKw29CAeXgcg5lL5bAcowTO2gjEoHLQPSAJ63Hw", "domain": "cpsdev.us.auth0.com", "path": "/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "did_compat", "value": "s%3Av0%3A2ef0e4ff-7110-42ec-9e33-2c691c7dee3e.fYlXKKw29CAeXgcg5lL5bAcowTO2gjEoHLQPSAJ63Hw", "domain": "cpsdev.us.auth0.com", "path": "/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "Lax"}, {"name": "auth0", "value": "s%3At1mTz-abXgUYhfbbEWl_905nnLyj8f9_.vH9WfVrtwJkfAMm61R8qmaY6i7%2F6yjqJIKlTkAjcSk4", "domain": "cpsdev.us.auth0.com", "path": "/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "auth0_compat", "value": "s%3At1mTz-abXgUYhfbbEWl_905nnLyj8f9_.vH9WfVrtwJkfAMm61R8qmaY6i7%2F6yjqJIKlTkAjcSk4", "domain": "cpsdev.us.auth0.com", "path": "/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "Lax"}, {"name": "ai_session", "value": "nCXCqfq/GSBfUFx8fvaqJR|1758038195743|**********812", "domain": "sqa-app.ecps.ca", "path": "/", "expires": 1758040113.813516, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "_legacy_auth0.SgHnwewE6BB3pgB4UXbF93el2rLf4Edf.is.authenticated", "value": "true", "domain": "sqa-app.ecps.ca", "path": "/", "expires": **********, "httpOnly": false, "secure": true, "sameSite": "Lax"}, {"name": "auth0.SgHnwewE6BB3pgB4UXbF93el2rLf4Edf.is.authenticated", "value": "true", "domain": "sqa-app.ecps.ca", "path": "/", "expires": **********, "httpOnly": false, "secure": true, "sameSite": "None"}], "origins": [{"origin": "https://sqa-app.ecps.ca", "localStorage": [{"name": "@@auth0spajs@@::SgHnwewE6BB3pgB4UXbF93el2rLf4Edf::https://administrationportal-sqa/api::openid profile email", "value": "{\"body\":{\"access_token\":\"****************************************************************************.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"scope\":\"openid profile email\",\"expires_in\":86400,\"token_type\":\"Bearer\",\"audience\":\"https://administrationportal-sqa/api\",\"oauthTokenScope\":\"openid profile email\",\"client_id\":\"SgHnwewE6BB3pgB4UXbF93el2rLf4Edf\"},\"expiresAt\":**********}"}, {"name": "page_size", "value": "60"}, {"name": "@@auth0spajs@@::SgHnwewE6BB3pgB4UXbF93el2rLf4Edf::@@user@@", "value": "{\"id_token\":\"****************************************************************************.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"decodedToken\":{\"encoded\":{\"header\":\"****************************************************************************\",\"payload\":\"*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"signature\":\"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"},\"header\":{\"alg\":\"RS256\",\"typ\":\"JWT\",\"kid\":\"mx-ZOP-FrpjeZhjFR3g-X\"},\"claims\":{\"__raw\":\"****************************************************************************.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"sAMAccountName\":\"********\",\"given_name\":\"Yoshi\",\"family_name\":\"Carroll\",\"nickname\":\"********\",\"name\":\"zymogyfrench\",\"picture\":\"https://s.gravatar.com/avatar/86a38f1edd349bfc261fca1772dee8b5?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fzy.png\",\"updated_at\":\"2025-09-16T15:58:30.734Z\",\"email\":\"<EMAIL>\",\"iss\":\"https://cpsdev.us.auth0.com/\",\"aud\":\"SgHnwewE6BB3pgB4UXbF93el2rLf4Edf\",\"sub\":\"ad|AD-arahlthcare|00f0f6ef-c7a7-425d-aac5-603ca4cc366d\",\"iat\":**********,\"exp\":**********,\"sid\":\"FAk2MefvpLKWYLjOgPub5pKgvbqEeytw\",\"nonce\":\"ZklQUTVZdTd+aWJkQk4wZ0FLUU14NzFnUVJDMFdtd1dScjlIY0pKVDF0NA==\"},\"user\":{\"sAMAccountName\":\"********\",\"given_name\":\"Yoshi\",\"family_name\":\"Carroll\",\"nickname\":\"********\",\"name\":\"zymogyfrench\",\"picture\":\"https://s.gravatar.com/avatar/86a38f1edd349bfc261fca1772dee8b5?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fzy.png\",\"updated_at\":\"2025-09-16T15:58:30.734Z\",\"email\":\"<EMAIL>\",\"sub\":\"ad|AD-arahlthcare|00f0f6ef-c7a7-425d-aac5-603ca4cc366d\"}}}"}, {"name": "loglevel", "value": "WARN"}]}]}