import { expect, type Page } from '@playwright/test';
import { AxeBuilder } from '@axe-core/playwright';
import PlaywrightWrapper from "../helper/wrapper/PlaywrightWrappers";
import { clickElement, fillElement, forceClickElement, waitAndClickElement } from "../helper/wrapper/actions";
import { setTimeout } from 'timers/promises';
import { fixture } from "../hooks/pageFixture";
import Assert from "../helper/wrapper/assert";

export default class FrenchUserPage {
    private base: PlaywrightWrapper;
    private assert: Assert;

    constructor(private page: Page) {
        this.base = new PlaywrightWrapper(page);
        this.assert = new Assert(page);
    }

    // French text constants for better maintainability
    private readonly FRENCH_TEXTS = {
        // Common user fields
        USER_FIELDS: [
            "Prénom",
            "Nom de famille",
            "Langue",
            "Poste",
            "Marque",
            "Nom d'utilisateur",
            "Groupe principal d'établissements",
            "Établissement principal",
            "Gestionnaire de compte",
            "Statut",
            "Créé par",
            "Date de création",
            "Dernière modification par",
            "Date de dernière modification",
            "Rôle hérité",
            "Nouveaux rôles",
            "Notes",
            "Numéros de téléphone",
            "Adresses"
        ],


        //  "de compte CPS", not apearing in edit page

        // Common buttons
        COMMON_BUTTONS: {
            GO_BACK: "Go back",
            SEARCH: "Rechercher",
            FILTER_BY: "Filtrer par",
            RESET: "Réinitialiser les",
            SUBMIT: "Soumettre",
            SAVE: "Enregistrer",
            EDIT: "Modifier",
            ADD_USER: "Ajouter Utilisateur"
        },

        // Page headings
        HEADINGS: {
            USER_INFO: "Informations utilisateur",
            EDIT_USER: "Modifier l'utilisateur",
            ADD_USER: "Ajouter un utilisateur",
            ROLE_INFO: "Informations du rôle",
            ADD_ROLE: "Ajouter un nouveau rôle",
            COPY_ROLE: "Copier le rôle",
            EDIT_ROLE: "Modifier le rôle"
        },

        // Error messages
        ERRORS: {
            FIRST_NAME_REQUIRED: "Le prénom est requis",
            LAST_NAME_REQUIRED: "Le nom de famille est requis",
            COMPANY_REQUIRED: "L'entreprise est requise",
            LANGUAGE_REQUIRED: "La langue est requise",
            USERNAME_REQUIRED: "Le nom d'utilisateur est",
            PASSWORD_REQUIRED: "Le mot de passe est requis",
            ROLE_REQUIRED: "Le rôle est requis",
            CONTACT_REQUIRED: "L'adresse e-mail ou le numéro"
        },

        // Validation messages
        VALIDATIONS: {
            INVALID_DATA: "Veuillez entrer des données valides avant de soumettre le formulaire.",
            CONTACT_REQUIREMENT: "Veuillez fournir au moins un moyen de contact — Email ou Numéro de téléphone.",
            FIRST_NAME_MAX: "Le prénom ne doit pas dépasser 50 caractères",
            LAST_NAME_MAX: "Le nom de famille ne doit pas dépasser 50 caractères",
            USERNAME_EXISTS: "Ce nom d'utilisateur est déjà utilisé. Veuillez en entrer un autre.",
            USERNAME_MIN: "Le nom d'utilisateur doit contenir au moins 3 caractères",
            PASSWORD_MIN: "Le mot de passe doit contenir au moins 12 caractères",
            PASSWORD_UPPERCASE: "Le mot de passe doit contenir au moins une lettre majuscule"
        },

        // Dropdown options
        DROPDOWN_OPTIONS: {
            PHONE_TYPES: ["Principal", "Travail", "Cellulaire", "Télécopieur", "Maison", "Pagette"],
            COUNTRIES: ["Canada", "USA"],
            CANADIAN_PROVINCES: ["AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"],
            US_STATES: ["PA", "FL", "GA", "HI", "IA", "ID", "IL", "IN", "KS", "KY", "LA", "MA", "MD", "ME", "MI", "MN", "MO", "MS", "MT", "NE", "NC", "ND", "NH", "NJ", "NM", "NY", "NV", "OH", "OK", "RI", "SC", "SD", "TN", "TX", "UT", "VA", "VT", "WA", "WI", "OR", "WV", "WY", "AK", "AL", "AR", "AZ", "CA", "CO", "CT", "DE"],
            THIRD_PARTY_APPS: ["Purchasing", "Dashboard", "HUBERT", "PartsTown"]
        },

        // Facility-related texts
        FACILITY: {
            COMMON_FIELDS: [
                "Source",
                "Marque",
                "Statut",
                "Date de création",
                "Créé par",
                "Date de dernière modification",
                "Dernière modification par",
                "Fichier logo"
            ],
            FACILITY_FIELDS: [
                "Nom de l'établissement",
                "Groupe d'établissement",
                "Gestionnaire de compte",
                "Type d'établissement",
                "Numéro de division",
                "Numéro de client",
                "Partager les menus en dehors deGroupe d'établissement principal"
            ],
            GROUP_FIELDS: [
                "Nom du groupe"
            ],
            SECTIONS: [
                "Détails de l'établissement",
                "Zone d'accès au site",
                "Adresse",
                "Utilisateurs du portail",
                "Notes d'administration"
            ],
            SWITCHES: [
                "TéléchargerMenu",
                "ImprimerlaRecette",
                "ÉchelleRecette",
                "RecetteBinder",
                "TéléchargerTheme",
                "DownloadEducation",
                "eAchat",
                "Dashboard",
                "RecetteImport",
                "HC Procurement",
                "ePurchasing v2-Staging",
                "HUBERT",
                "Enable RDS Sync",
                "PartsTown"
            ],
            HEADINGS: {
                CREATE_GROUP: "Ajouter un nouveau groupe d'établissement",
                EDIT_GROUP: "Modifier le groupe d'établissement",
                VIEW_GROUP: "Détails du groupe d'établissement",
                FACILITY_DETAILS: "Détails de l'établissement",
                EDIT_FACILITY: "Modifier l'établissement"
            },
            GROUP_BUTTONS: [
                "Détails du groupe d'é",
                "Accès à la zone du site",
                "Notes d'administration"
            ],
            EDIT_GROUP_BUTTONS: [
                "Détails du groupe d'é",
                "Accès à la zone du site",
                "Établissements associés",
                "Notes d'administration"
            ],
            FILTER_FIELDS: [
                "Filtrer par",
                "Rechercher par",
                "Groupe d'établissement",
                "Établissement",
                "Numéro de division",
                "Numéro de client"
            ],
            STATUS_OPTIONS: ["Actif", "Inactif"],
            MESSAGES: {
                NO_DATA: "Aucune donnée trouvée",
                APPLY_FILTERS: "Veuillez appliquer des filtres pour afficher les données."
            },
            ADDRESS_FIELDS: [
                "Address Type",
                "Pays",
                "Address Line 1",
                "Address Line 2",
                "Ville",
                "Province/State",
                "Postal Code"
            ],
            VIEW_GROUP_BUTTONS: [
                "Détails du groupe d'é",
                "Zone d'accès au site",
                "Établissements associés",
                "Notes d'administration"
            ]
        }
    };

    private elements = {
        searchUser: this.page.getByRole('button', { name: 'Rechercher' }),
        users: this.page.getByRole('link', { name: 'Utilisateurs' }),
        facilities: this.page.getByRole('link', { name: 'Établissements' }),
        roles: this.page.getByRole('link', { name: 'Rôles' }),
        filterByUser: this.page.getByRole('button', { name: 'Filtrer par' }),
        firstRow: this.page.getByRole('button', { name: "Voir l'utilisateur" }).first(),
        editUser: this.page.getByRole('button', { name: 'Modifier' }),
        addNewUser: this.page.getByRole('button', { name: 'Ajouter Utilisateur' }),
        selectBrand: this.page.locator('div').filter({ hasText: /^Marque\*Sélectionnez-en un$/ }).getByLabel('Sélectionnez-en un'),
        clickBrand: this.page.getByRole('menuitem', { name: 'CPS' }),
        addNewPhone: this.page.locator('div').filter({ hasText: /^Numéros de téléphone\+ AJOUTER$/ }).getByLabel('+ AJOUTER'),
        addNwadd: this.page.locator('div').filter({ hasText: /^Adresses\+ AJOUTER$/ }).getByLabel('+ AJOUTER'),
        addType: this.page.getByRole('button', { name: 'Add New', exact: true }),
        funcArea: this.page.getByRole('combobox', { name: 'Domaine fonctionnel' }),
        discardChanges: this.page.getByLabel('Yes, Discard changes'),
        firstRowRoleView: this.page.locator("button[aria-label='View Role']").nth(1),
        firstRowRoleEdit: this.page.locator("button[aria-label='Edit Role']").nth(1),
        firstRowRoleCopy: this.page.locator("button[aria-label='Duplicate Role']").nth(1),
        firstRowRoleDelete: this.page.locator("button[aria-label='Delete Role']").first(),
        createRole: this.page.getByRole('button', { name: 'Créer un nouveau rôle' }),
        assignUs: this.page.getByRole('button', { name: 'Plus Assigner' }),
        searchUs: this.page.getByRole('button', { name: 'Rechercher' }),
        closeSearchP: this.page.getByRole('button', { name: 'Close Cross' }),
        addNewFacG: this.page.getByRole('button', { name: 'Ajouter un nouveau groupe d\'é' }),
        back: this.page.getByLabel('Go back'),
        addNewFac: this.page.getByRole('button', { name: 'Ajouter un nouvel é', exact: true }),
        facedit: this.page.locator('#facilities'),
        firstRowFGView: this.page.locator("button[aria-label='View Facility Group']").first(),
        firstRowFGEdit: this.page.locator("button[aria-label='Edit Facility Group']").first(),
        firstRowF: this.page.locator("button[aria-label='View Facility']").nth(1),
        firstNameInput: this.page.getByRole('textbox', { name: 'Prénom' }),
        lastNameInput: this.page.getByRole('textbox', { name: 'Nom de famille' }),
        usernameInput: this.page.getByRole('textbox', { name: 'Nom d\'utilisateur' }),
        passwordInput: this.page.getByRole('textbox', { name: 'Mot de passe' }),
        roleSelectionButton1: this.page.locator("div[class$='items-baseline'] button").first(),
        submitButton: this.page.getByRole('button', { name: 'Soumettre' }),
        backButton: this.page.getByRole('button', { name: 'Go back' }),
        unassignUserButton: this.page.getByRole('button', { name: /^Minus Désassigner/ }),
        selectAllCheckbox: this.page.getByRole('checkbox', { name: 'Tout sélectionner' }),
        addThirdPartyButton: this.page.getByRole('button', { name: 'Ajouter nouveau', exact: true }),
        editF: this.page.getByRole('button', { name: 'Modifier', exact: true })

    }

    private verifyExactText = (text: string) => this.page.getByText(text, { exact: true }).first();
    private verifyButton = (name: string) => this.page.getByRole('button', { name });
    private roleSelect = (role: string) => this.page.getByRole('menuitem', { name: role, exact: true });

    // Helper methods for common operations
    private async verifyTextArray(texts: string[], logPrefix: string = ""): Promise<void> {
        for (const text of texts) {
            await this.assert.assertElementContainsText(this.verifyExactText(text), text);
            if (logPrefix) {
                fixture.logger.info(`${logPrefix}: "${text}" is visible`);
            }
        }
    }

    private async verifyButtonArray(buttons: string[]): Promise<void> {
        for (const btn of buttons) {
            await this.assert.assertElementIsVisible(this.verifyButton(btn));
        }
    }

    private async verifyMainContainsTexts(texts: string[]): Promise<void> {
        for (const text of texts) {
            await expect(this.page.getByRole('main')).toContainText(text);
            fixture.logger.info(`Main text "${text}" is visible`);
        }
    }

    private async performRoleValidation(role: string, expectedErrors: string[]): Promise<void> {
        await clickElement(this.elements.roleSelectionButton1);
        await clickElement(this.roleSelect(role));
        await clickElement(this.elements.submitButton);

        for (const error of expectedErrors) {
            await this.assert.assertElementContainsText(this.verifyExactText(error), error);
        }
    }

    private async validateDropdownAndEscape(dropdownLocator: any, expectedOptions: string[], ordered: boolean = false): Promise<void> {
        await this.base.validateDropdownOptions(dropdownLocator, expectedOptions, ordered);
        await this.base.escapeOnDropdown();
    }

    private async validateAddressFields(): Promise<void> {
        const addressErrors = [
            'La ligne d\'adresse 1 est requise',
            'La ville est requise',
            'Le code postal est requis',
            'La province est requise'
        ];

        await clickElement(this.elements.addNwadd);
        await clickElement(this.elements.submitButton);

        for (const error of addressErrors) {
            await this.assert.assertElementContainsText(this.verifyExactText(error), error);
        }
    }

    private async verifyFacilitySwitches(): Promise<void> {
        for (const switchName of this.FRENCH_TEXTS.FACILITY.SWITCHES) {
            await expect(this.page.getByRole('main')).toContainText(switchName);
        }
    }

    private async verifyFacilityCommonFields(): Promise<void> {
        await this.verifyMainContainsTexts([
            ...this.FRENCH_TEXTS.FACILITY.COMMON_FIELDS,
            ...this.FRENCH_TEXTS.FACILITY.SECTIONS
        ]);
    }

    private async verifyFacilityGroupCommonElements(): Promise<void> {
        // Verify common facility group fields
        await this.verifyMainContainsTexts([
            ...this.FRENCH_TEXTS.FACILITY.COMMON_FIELDS.slice(0, 4), // Source, Marque, Statut, Date de création
            ...this.FRENCH_TEXTS.FACILITY.GROUP_FIELDS
        ]);

        // Verify facility switches
        await this.verifyFacilitySwitches();
    }

    private async verifyFacilityGroupButtons(buttons: string[]): Promise<void> {
        await this.verifyButtonArray([
            this.FRENCH_TEXTS.COMMON_BUTTONS.RESET,
            ...buttons
        ]);
    }

    private async verifyFacilityEditElements(): Promise<void> {
        // Verify facility sections
        await this.verifyMainContainsTexts(this.FRENCH_TEXTS.FACILITY.SECTIONS);

        // Verify common facility fields
        await this.verifyMainContainsTexts([
            ...this.FRENCH_TEXTS.FACILITY.COMMON_FIELDS,
            ...this.FRENCH_TEXTS.FACILITY.FACILITY_FIELDS
        ]);

        // Verify address fields
        await this.verifyMainContainsTexts(this.FRENCH_TEXTS.FACILITY.ADDRESS_FIELDS);

        // Verify facility switches
        await this.verifyFacilitySwitches();
    }

    private async verifyFacilitySwitchesToggles(): Promise<void> {
        // Verify switches as toggles (for create facility page)
        for (const switchName of this.FRENCH_TEXTS.FACILITY.SWITCHES) {
            await expect(this.page.getByRole('switch', { name: `${switchName} toggle` })).toBeVisible();
        }
    }
    async LogAndVerifyContent(_page: Page, str: string) {
        fixture.logger.info(str + "  French Content verified");
        //expect(accessibilityScanResults.violations).toEqual([]);
    }
    async userPageVerify(page: Page) {
        await clickElement(this.elements.searchUser);
        await clickElement(this.elements.filterByUser);
        await this.verifyUserFilterByButtonValidation();
        await this.LogAndVerifyContent(page, "search user");
        await clickElement(this.elements.firstRow);
        await this.LogAndVerifyContent(page, "view user");
        await this.viewUser({ verifyFromView: true });
        await clickElement(this.elements.editUser);
        await this.editUser();
        await this.LogAndVerifyContent(page, "edit user");
        await clickElement(this.elements.users);
        await clickElement(this.elements.addNewUser);
        await this.verifyCreateUserValidations();
        await this.LogAndVerifyContent(page, "create user");
    }
    async rolePageVerify(page: Page) {
        await clickElement(this.elements.roles);
        //await clickElement(this.elements.discardChanges);
        await this.LogAndVerifyContent(page, "list roles");
        await clickElement(this.elements.firstRowRoleView);
        await this.LogAndVerifyContent(page, "view roles");
        await this.verifyRoleViewPageButtons();
        await clickElement(this.elements.backButton);
        await clickElement(this.elements.firstRowRoleEdit);
        await this.verifyEditRolePageButtons();
        await this.LogAndVerifyContent(page, "edit roles");
        await forceClickElement(this.elements.selectAllCheckbox);
        await clickElement(this.elements.unassignUserButton);
        await this.verifyUnassignUserPopup();
        await clickElement(this.elements.backButton);
        await clickElement(this.elements.firstRowRoleCopy);
        await this.verifyCopyRolePageButtons();
        await this.LogAndVerifyContent(page, "copy roles");
        await clickElement(this.elements.backButton);
        await clickElement(this.elements.firstRowRoleDelete);
        await this.verifyConfirmDeletePopup();
        await this.LogAndVerifyContent(page, "delete role");
        await clickElement(this.elements.roles);
        await clickElement(this.elements.createRole);
        await this.verifyCreateRolePageButtons();
        await this.LogAndVerifyContent(page, "create roles");
        await clickElement(this.elements.assignUs);
        await clickElement(this.elements.searchUs);
        await this.LogAndVerifyContent(page, "assign user roles");
        await clickElement(this.elements.closeSearchP);
    }
    async facilitiesPageVerify(page: Page) {
        await clickElement(this.elements.facilities);
        await this.verifyFacilityFilterByButtonValidation();
        await clickElement(this.elements.searchUser);
        await clickElement(this.elements.filterByUser);
        await this.LogAndVerifyContent(page, "Search FGs");
        await clickElement(this.elements.firstRowFGView);
        await this.verifyViewFacilityGroupPageButtons();
        await this.LogAndVerifyContent(page, "view FGs");
        await clickElement(this.elements.editF);
        await this.verifyEditFacilityGroupPageButtons();
        await this.LogAndVerifyContent(page, "edit FGs");
        await clickElement(this.elements.facilities);
        await clickElement(this.elements.addNewFacG);
        await this.verifyCreateFacilityGroupPageButtons();
        await this.LogAndVerifyContent(page, "Add FGs");
        await clickElement(this.elements.back);
        await clickElement(this.elements.addNewFac);
        await this.verifyCreateFacilitiesPageButtons();
        await this.LogAndVerifyContent(page, "Add FG");
        await clickElement(this.elements.back);
        await clickElement(this.elements.facedit);
        await clickElement(this.elements.searchUser);
        await clickElement(this.elements.firstRowF);
        await this.verifyViewFacilitiesPageButtons();
        await this.LogAndVerifyContent(page, "view Facility");
        await clickElement(this.elements.editUser);
        await this.verifyEditFacilitiesPageButtons();
        await this.LogAndVerifyContent(page, "edit Facility");
    }
    async CheckAllPagesAreInFrench() {
        await this.base.goto(process.env.TESTURL);
        await setTimeout(5000);
        if (this.page.url() != process.env.TESTURL) {
            await this.base.commonLogin("Fr");
        }
        //await this.base.waitForResponse('api/MasterData/facilities/filter');
        const page: Page = fixture.page;
        await this.rolePageVerify(page);
        await this.facilitiesPageVerify(page);
        await this.userPageVerify(page);
    }



    // verify user Filter by names 

    async verifyUserFilterByButtonValidation() {
        await expect(this.page.getByRole('main')).toContainText('Filtrer par');
        await expect(this.page.getByRole('main')).toContainText('Marque');
        await expect(this.page.getByRole('main')).toContainText('Nom');
        await expect(this.page.getByRole('main')).toContainText('Nom d\'utilisateur');
        await expect(this.page.getByRole('main')).toContainText('Email');
        await expect(this.page.getByRole('main')).toContainText('Groupe d\'établissement');
        await expect(this.page.getByRole('main')).toContainText('Établissement');
        await expect(this.page.getByRole('main')).toContainText('Accès à l\'application 3ᵉ partie');
        await expect(this.page.getByRole('main')).toContainText('Statut');
        await expect(this.page.getByRole('main')).toContainText('Contact CIP');
        await expect(this.page.getByRole('main')).toContainText('Multi-unité');
        await expect(this.page.locator('#checkbox-group-statut-actif-label')).toContainText('Actif');
        await expect(this.page.locator('#checkbox-group-statut-inactif-label')).toContainText('Inactif');
        await expect(this.page.locator('#checkbox-group-contact-cip-oui-label')).toContainText('Oui');
        await expect(this.page.locator('#checkbox-group-contact-cip-non-label')).toContainText('Non');
        await expect(this.page.locator('[id="checkbox-group-multi-unité-oui-label"]')).toContainText('Oui');
        await expect(this.page.locator('[id="checkbox-group-multi-unité-non-label"]')).toContainText('Non');
        await expect(this.page.getByRole('button', { name: 'Rechercher' })).toBeVisible();
        await expect(this.page.getByRole('button', { name: 'Réinitialiser' })).toBeVisible();
        await expect(this.page.getByRole('button', { name: 'Ajouter Utilisateur' })).toBeVisible();
        await this.page.getByRole('textbox', { name: 'Nom d\'utilisateur' }).fill('765456543');
        await this.page.getByRole('button', { name: 'Rechercher' }).click();
        await expect(this.page.getByRole('paragraph')).toContainText('Aucune donnée trouvée');
        await clickElement(this.elements.filterByUser);
        await this.page.getByRole('button', { name: 'Réinitialiser' }).click();
        await this.page.getByRole('button', { name: 'Rechercher' }).click();
        await this.base.waitForResponse("filter");
    }

    //view page validations
    async viewUser(options?: { verifyFromView?: boolean }) {
        if (!options?.verifyFromView) {
            await clickElement(this.elements.firstRow);
            await this.base.waitForResponse("detail");
        }
        await expect(this.page.getByRole('heading')).toContainText(this.FRENCH_TEXTS.HEADINGS.USER_INFO);

        // Verify user fields using helper method
        await this.verifyTextArray(this.FRENCH_TEXTS.USER_FIELDS);

        // For View Page
        //await this.base.verifyUserCreateorModifydateFr(false);
        fixture.logger.info("View user page verified successfully");

        const expectedButtons = [
            this.FRENCH_TEXTS.COMMON_BUTTONS.GO_BACK,
            this.FRENCH_TEXTS.COMMON_BUTTONS.EDIT,
            "Détails de l\'utilisateur",
            "Rôles utilisateur",
            "Détails de contact",
            "Notes d\'administration"
        ];
        await this.verifyButtonArray(expectedButtons);
    }

    //edit page validations
    async editUser() {
        await this.page.waitForLoadState("domcontentloaded");
        await setTimeout(500);
        await expect(this.page.getByRole('heading')).toContainText(this.FRENCH_TEXTS.HEADINGS.EDIT_USER);

        // Verify user fields using helper method
        await this.verifyTextArray(this.FRENCH_TEXTS.USER_FIELDS);

        // For View Page
        // await this.base.verifyUserCreateorModifydateFr(false);
        fixture.logger.info("Edit user page verified successfully");

        await this.base.verifyDisabledElementsCount(4, "Edit User page", 'greaterThan');

        const expectedButtons = [
            this.FRENCH_TEXTS.COMMON_BUTTONS.GO_BACK,
            this.FRENCH_TEXTS.COMMON_BUTTONS.RESET,
            this.FRENCH_TEXTS.COMMON_BUTTONS.SAVE,
            "Détails de l\'utilisateur",
            "Rôles utilisateur",
            "Détails de contact",
            "Notes d\'administration"
        ];
        await this.verifyButtonArray(expectedButtons);
    }

    //create user form validations

    async verifyCreateUserValidations() {
        const createUserTexts = [
            "Détails de l\'utilisateur",
            "Domaine fonctionnel",
            "Rôle utilisateur",
            "Nouveaux rôles",
            "Détails de contact",
            "Identifiants d\'autorisation tiers",
            "Notes d\'administration",
            "Notes",
            ...this.FRENCH_TEXTS.USER_FIELDS.slice(0, 8) // First 8 common fields
        ];

        await this.verifyPageUI({
            headingText: this.FRENCH_TEXTS.HEADINGS.ADD_USER,
            requiredMainTexts: createUserTexts,
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: this.FRENCH_TEXTS.COMMON_BUTTONS.RESET }), label: "Button 'Reset Changes in french Réinitialiser les'" },
                { locator: this.page.getByRole('button', { name: this.FRENCH_TEXTS.COMMON_BUTTONS.SUBMIT }), label: "Button 'Soumettre'" },
                { locator: this.page.getByRole('button', { name: 'Détails de l\'utilisateur' }), label: "Button 'Détails de l\'utilisateur'" },
                { locator: this.page.getByRole('button', { name: 'Rôles utilisateur' }), label: "Button 'Rôles utilisateur'" },
                { locator: this.page.getByRole('button', { name: 'Détails de contact' }), label: "Button 'Détails de contact'" },
                { locator: this.page.getByRole('button', { name: 'Identifiants d\'autorisation' }), label: "Button 'Identifiants d\'autorisation'" },
                { locator: this.page.getByRole('button', { name: 'Notes d\'administration' }), label: "Button 'Notes d\'administration'" },
                { locator: this.page.locator('div').filter({ hasText: /^Actif$/ }).getByRole('switch'), label: "Active switch" },
            ]
        });


        await expect(this.page.getByRole('main')).toContainText('Email');
        await expect(this.page.getByRole('paragraph')).toContainText(this.FRENCH_TEXTS.VALIDATIONS.CONTACT_REQUIREMENT);

        await this.base.verifyDisabledElementsCount(2, "Create User page", 'equal', this.page.locator('div[data-state="open"] button[data-slot="popover-trigger"]:disabled'));
        await this.assert.assertElementContainsText(this.verifyExactText(this.FRENCH_TEXTS.VALIDATIONS.CONTACT_REQUIREMENT), this.FRENCH_TEXTS.VALIDATIONS.CONTACT_REQUIREMENT);
        await this.assert.assertElementContainsText(this.verifyExactText('Email'), 'Email');
        await this.page.getByRole('button', { name: this.FRENCH_TEXTS.COMMON_BUTTONS.SUBMIT }).click();

        await this.base.verifyToast(this.FRENCH_TEXTS.VALIDATIONS.INVALID_DATA);

        // Define all required error texts using constants
        const requiredMainErrorTexts: string[] = [
            this.FRENCH_TEXTS.ERRORS.FIRST_NAME_REQUIRED,
            this.FRENCH_TEXTS.ERRORS.LAST_NAME_REQUIRED,
            this.FRENCH_TEXTS.ERRORS.COMPANY_REQUIRED,
            this.FRENCH_TEXTS.ERRORS.LANGUAGE_REQUIRED,
            'Le groupe d\'établissements',
            'L\'établissement est requis',
            'Le domaine fonctionnel est',
            this.FRENCH_TEXTS.ERRORS.USERNAME_REQUIRED,
            this.FRENCH_TEXTS.ERRORS.PASSWORD_REQUIRED,
            this.FRENCH_TEXTS.ERRORS.ROLE_REQUIRED,
            this.FRENCH_TEXTS.ERRORS.CONTACT_REQUIRED,
        ];

        // Use helper method to verify main contains texts
        await this.verifyMainContainsTexts(requiredMainErrorTexts);

        // Test field validations with long text
        const longText = 'jdkfhsldfhkdskfjksdhkfhsdkfsldjsnfkndfsdfkjdkfhsldfhkdskfjksdhkfhsdkfsldjsnfkndfsdfk';
        await fillElement(this.elements.firstNameInput, longText);
        await fillElement(this.elements.lastNameInput, longText);
        await this.assert.assertElementContainsText(this.verifyExactText(this.FRENCH_TEXTS.VALIDATIONS.FIRST_NAME_MAX), this.FRENCH_TEXTS.VALIDATIONS.FIRST_NAME_MAX);
        await this.assert.assertElementContainsText(this.verifyExactText(this.FRENCH_TEXTS.VALIDATIONS.LAST_NAME_MAX), this.FRENCH_TEXTS.VALIDATIONS.LAST_NAME_MAX);

        // Test username validations
        await fillElement(this.elements.usernameInput, 'testuser');
        await this.assert.assertElementContainsText(this.verifyExactText(this.FRENCH_TEXTS.VALIDATIONS.USERNAME_EXISTS), this.FRENCH_TEXTS.VALIDATIONS.USERNAME_EXISTS);
        await this.elements.usernameInput.clear();
        await fillElement(this.elements.usernameInput, 'tr');
        await this.assert.assertElementContainsText(this.verifyExactText(this.FRENCH_TEXTS.VALIDATIONS.USERNAME_MIN), this.FRENCH_TEXTS.VALIDATIONS.USERNAME_MIN);

        // Test password validations
        await fillElement(this.elements.passwordInput, 'tr');
        await this.assert.assertElementContainsText(this.verifyExactText(this.FRENCH_TEXTS.VALIDATIONS.PASSWORD_MIN), this.FRENCH_TEXTS.VALIDATIONS.PASSWORD_MIN);
        await fillElement(this.elements.passwordInput, '*************');
        await this.assert.assertElementContainsText(this.verifyExactText(this.FRENCH_TEXTS.VALIDATIONS.PASSWORD_UPPERCASE), this.FRENCH_TEXTS.VALIDATIONS.PASSWORD_UPPERCASE);


        // Test role-specific validations using helper method
        await this.performRoleValidation('Account Manager', [
            'Le vice-président des opérations est requis',
            'La province principale est requise'
        ]);

        await this.performRoleValidation('Regional Director', [
            'La province principale est requise'
        ]);

        await this.performRoleValidation('Client Group Admin', [
            'Le gestionnaire de compte est requis',
            'La province principale est requise'
        ]);

        await this.performRoleValidation('Education Coordinator', [
            'Au moins un département doit être sélectionné',
            'Au moins un établissement doit être sélectionné'
        ]);

        await clickElement(this.elements.addNewPhone);
        await clickElement(this.elements.submitButton);
        await this.assert.assertElementContainsText(this.verifyExactText('Le numéro de téléphone est requis'), 'Le numéro de téléphone est requis');

        // Validate address fields using helper method
        await this.validateAddressFields();

        // Validate phone number types dropdown
        await this.validateDropdownAndEscape(
            this.page.getByRole('row', { name: 'Principal United States/' }).getByLabel('Principal'),
            this.FRENCH_TEXTS.DROPDOWN_OPTIONS.PHONE_TYPES,
            true
        );

        // Validate country dropdown
        await this.validateDropdownAndEscape(
            this.page.getByRole('button', { name: 'Canada', exact: true }),
            this.FRENCH_TEXTS.DROPDOWN_OPTIONS.COUNTRIES,
            true
        );

        // Validate Canadian provinces dropdown
        await this.validateDropdownAndEscape(
            this.page.getByRole('cell', { name: 'Sélectionnez-en un La' }).getByLabel('Sélectionnez-en un'),
            this.FRENCH_TEXTS.DROPDOWN_OPTIONS.CANADIAN_PROVINCES
        );

        // Switch to USA and validate US states
        await this.page.getByRole('button', { name: 'Canada', exact: true }).click();
        await this.page.getByRole('menuitem', { name: 'USA' }).click();
        await setTimeout(300);

        // Validate US states dropdown
        await this.validateDropdownAndEscape(
            this.page.getByRole('cell', { name: 'Sélectionnez-en un' }).getByLabel('Sélectionnez-en un'),
            this.FRENCH_TEXTS.DROPDOWN_OPTIONS.US_STATES
        );

        // Validate third party applications dropdown
        await clickElement(this.elements.addThirdPartyButton);
        await this.validateDropdownAndEscape(
            this.page.locator('td[class$=text-xs] button[data-slot="dropdown-menu-trigger"]'),
            this.FRENCH_TEXTS.DROPDOWN_OPTIONS.THIRD_PARTY_APPS
        );
        await clickElement(this.elements.submitButton);
        await this.assert.assertElementContainsText(this.verifyExactText('L\'application est requise'), 'L\'application est requise');
        await this.assert.assertElementContainsText(this.verifyExactText('Le nom d\'utilisateur est requis'), 'Le nom d\'utilisateur est requis');
        // clear all errors
        await this.base.verifyResetPopupFr();
        await this.page.getByRole('button', { name: 'Réinitialiser les' }).click();
        await this.page.getByRole('button', { name: 'Oui, continuer' }).click();
        //await expect(this.page.getByRole('alert')).toContainText('Les modifications ont été réinitialisées avec succès !');
        await this.base.verifyToast('Les modifications ont été réinitialisées avec succès !');

    }

    async verifyPageUI({
        headingText,
        requiredMainTexts = [],
        commonElements = [],
        pageSpecificElements = []
    }: {
        headingText: string;
        requiredMainTexts?: string[];
        commonElements?: { locator: any; label: string }[];
        pageSpecificElements?: { locator: any; label: string }[];
    }) {
        // Verify heading text
        await expect(this.page.getByRole('heading')).toContainText(headingText);
        //fixture.logger.info(` Heading "${headingText}" is visible`);

        //  Verify additional static texts inside main
        for (const text of requiredMainTexts) {
            await expect(this.page.getByRole('main')).toContainText(text);
            //fixture.logger.info(` Main text "${text}" is visible`);
        }

        //  Define default common elements (can be overridden if passed in)
        const defaultCommonElements = [
            { locator: this.page.getByRole('button', { name: 'Go back' }), label: "Button 'Go back'" },
        ];

        //  Merge everything
        const allElements = [...defaultCommonElements, ...commonElements, ...pageSpecificElements];

        //  Verify all UI elements with logging
        for (const { locator, label } of allElements) {
            await this.base.verifyWithLogging(locator, label);
        }
    }

    //Role Page Validations 


    async verifyUnassignUserPopup(): Promise<void> {
        await expect(this.page.locator('[role="alertdialog"] [data-slot="alert-dialog-description"]')).toContainText('Êtes-vous sûr de vouloir désassigner les utilisateurs sélectionnés ? Une fois désassignés, les utilisateurs perdront l\'accès aux modules et permissions du rôle');
        await expect(this.page.getByLabel('Désassigner les utilisateurs').locator('span')).toContainText('Désassigner les utilisateurs');
        await this.base.verifyWithLogging(this.page.locator('div[data-slot="alert-dialog-footer"]').getByRole('button', { name: 'Oui, désassigner' }), "Button 'Yes, Unassign in french Oui, désassigner'");
        await this.base.verifyWithLogging(this.page.locator('div[data-slot="alert-dialog-footer"]').getByRole('button', { name: 'Non, Annuler' }), "Button 'No, Cancelin french Non, Annuler'");
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'Close dialog' }), "Button 'Close dialog'");
        await this.page.getByRole('button', { name: 'Close dialog' }).click();
    }

    async verifyConfirmDeletePopup(): Promise<void> {
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'Close dialog' }), "Button 'Close dialog'");
        await this.base.verifyWithLogging(this.page.locator('div[data-slot="alert-dialog-footer"]').getByRole('button', { name: 'Oui, supprimer' }), "Button 'Yes, Delete in french Oui, supprimer'");
        await this.base.verifyWithLogging(this.page.locator('div[data-slot="alert-dialog-footer"]').getByRole('button', { name: 'Non, annuler' }), "Button 'No, Cancel in french Non, annuler'");
        await expect(this.page.getByLabel('Confirmation de suppression').locator('span')).toContainText('Confirmation de suppression');
        await expect(this.page.locator('[role="alertdialog"] [data-slot="alert-dialog-description"]')).toContainText('Êtes-vous sûr de vouloir supprimer ce rôle utilisateur ? Une fois supprimé, les données ne peuvent pas être récupérées.');
        await this.page.getByRole('button', { name: 'Close dialog' }).click();
    }

    //common role page
    async verifyRolePage({
        headingText,
        mainTexts = [],
        pageSpecificElements = []
    }: {
        headingText: string;
        mainTexts?: string[];
        pageSpecificElements?: { locator: any; label: string }[];
    }) {
        // Verify heading
        await expect(this.page.getByRole('heading')).toContainText(headingText);
        fixture.logger.info(`Header text: ${headingText}  is visible`);

        // Verify common "Assigned to"
        await expect(this.page.getByRole('main')).toContainText('Assigné à');

        // Verify additional main texts if provided
        for (const text of mainTexts) {
            await expect(this.page.getByRole('main')).toContainText(text);
            fixture.logger.info(`Main text: ${text}  is visible`);
        }

        // Define common buttons
        const commonButtons = [
            { locator: this.page.getByRole('button', { name: 'Go back' }), label: "Button 'Go back'" },
            { locator: this.page.getByRole('button', { name: 'Expand All' }), label: "Button 'Expand All'" },
            { locator: this.page.getByRole('button', { name: 'Collapse All' }), label: "Button 'Collapse All'" },
            { locator: this.page.getByRole('button', { name: 'Supplier toggle Supplier' }), label: "Button 'Supplier toggle Supplier'" },
            { locator: this.page.getByRole('button', { name: 'User Management toggle User' }), label: "Button 'User Management toggle User'" },
            { locator: this.page.getByRole('button', { name: 'Role Management toggle Role' }), label: "Button 'Role Management toggle Role'" },
            { locator: this.page.getByRole('button', { name: 'Facility Management toggle' }), label: "Button 'Facility Management toggle'" },
            { locator: this.page.getByRole('textbox', { name: 'Search permissions' }), label: "search input Box 'Search For permissions'" },
        ];

        // Merge page-specific + common
        const allElements = [...pageSpecificElements, ...commonButtons];

        // Verify all elements
        for (const { locator, label } of allElements) {
            await this.base.verifyWithLogging(locator, label);
        }
    }


    async verifyRoleViewPageButtons() {


        await this.verifyRolePage({
            headingText: 'Informations du rôle',
            mainTexts: ['Modules et permissions'],
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Export' }), label: "Button 'Export'" },
                { locator: this.page.getByRole('button', { name: 'Filter by' }), label: "Button 'Filter by'" },
                { locator: this.page.getByRole('button', { name: 'Modifier' }), label: "Button 'Edit in french Modifier'" },
            ]
        });


    }


    async verifyCreateRolePageButtons() {

        await this.verifyRolePage({
            headingText: 'Ajouter un nouveau rôle',
            mainTexts: ['Modules et permissions', 'Aucun utilisateur assigné'],
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Réinitialiser les' }), label: "Button 'Reset Changes in french Réinitialiser les modifications'" },
                { locator: this.page.getByRole('button', { name: 'Enregistrer et ajouter' }), label: "Button 'Save & Add in french Enregistrer et ajouter'" },
            ]
        });


    }

    async verifyCopyRolePageButtons() {
        await expect(this.page.getByRole('textbox', { name: 'Nom du rôle' })).toHaveValue(/.*(?: - Copier)+$/);;
        await expect(this.page.getByRole('textbox', { name: 'Description du rôle' })).toHaveValue(/.*(?: - Copier)+$/);;
        await expect(this.page.getByRole('main')).toContainText('Assigné à');
        await expect(this.page.getByRole('main')).toContainText('Aucun utilisateur assigné');
        await expect(this.page.getByRole('main')).toContainText('Modules et permissions');

        const elementsToVerify = [
            { locator: this.page.getByRole('button', { name: 'Go back' }), label: "Button 'Go back'" },
            { locator: this.page.getByRole('heading', { name: 'Copier le rôle' }), label: "Heading 'Copy Role in french Copier le rôle'" },
            { locator: this.page.getByRole('button', { name: 'Réinitialiser les' }), label: "Button 'Reset'" },
            { locator: this.page.getByRole('button', { name: 'Enregistrer et ajouter' }), label: "Button 'Save & Add in french Enregistrer et ajouter'" },
            { locator: this.page.getByRole('textbox', { name: 'Search permissions' }), label: "Textbox 'Search permissions'" },
            { locator: this.page.getByRole('button', { name: 'Expand All' }), label: "Button 'Expand All'" },
            { locator: this.page.getByRole('button', { name: 'Collapse All' }), label: "Button 'Collapse All'" },
            { locator: this.page.getByRole('button', { name: 'Supplier toggle Supplier' }), label: "Button 'Supplier toggle Supplier'" },
            { locator: this.page.getByRole('button', { name: 'User Management toggle User' }), label: "Button 'User Management toggle User'" },
            { locator: this.page.getByRole('button', { name: 'Role Management toggle Role' }), label: "Button 'Role Management toggle Role'" },
            { locator: this.page.getByRole('button', { name: 'Facility Management toggle' }), label: "Button 'Facility Management toggle'" },
        ];

        for (const { locator, label } of elementsToVerify) {
            await this.base.verifyWithLogging(locator, label);
        }


    }


    async verifyEditRolePageButtons() {
        await this.verifyRolePage({
            headingText: 'Modifier le rôle',
            mainTexts: ['Modules et permissions'],
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Filter by' }), label: "Button 'Filter by'" },
                { locator: this.page.getByRole('button', { name: 'Export' }), label: "Button 'Export'" },
                { locator: this.page.getByRole('button', { name: 'Enregistrer' }), label: "Button 'Save'" },
                { locator: this.page.getByRole('button', { name: 'Réinitialiser les' }), label: "Button 'Reset Changes'" },
            ]
        });

    }




    //facility page validations 

    async verifyFacilitiesPageButtons() {
    }



    async verifyCreateFacilitiesPageButtons() {
        // Verify action buttons
        await this.verifyButtonArray([
            this.FRENCH_TEXTS.COMMON_BUTTONS.RESET,
            this.FRENCH_TEXTS.COMMON_BUTTONS.SUBMIT
        ]);

        // Verify active status element
        await expect(this.page.locator('div').filter({ hasText: /^Actif$/ })).toBeVisible();

        // Verify all facility switches as toggles
        await this.verifyFacilitySwitchesToggles();
    }

    async verifyEditFacilitiesPageButtons() {
        // Verify headings
        await expect(this.page.getByRole('heading')).toContainText(this.FRENCH_TEXTS.FACILITY.HEADINGS.FACILITY_DETAILS);
        await expect(this.page.getByRole('heading')).toContainText(this.FRENCH_TEXTS.FACILITY.HEADINGS.EDIT_FACILITY);

        // Verify action buttons
        await this.verifyButtonArray([
            this.FRENCH_TEXTS.COMMON_BUTTONS.RESET,
            this.FRENCH_TEXTS.COMMON_BUTTONS.SAVE
        ]);

        // Verify all facility edit elements using helper method
        await this.verifyFacilityEditElements();
    }

    async verifyViewFacilitiesPageButtons() {
        await expect(this.page.getByRole('heading')).toContainText('Détails de l\'établissement');
        await expect(this.page.getByRole('button', { name: this.FRENCH_TEXTS.COMMON_BUTTONS.EDIT })).toBeVisible();

        // Verify common facility fields and sections
        await this.verifyFacilityCommonFields();

        // Verify facility-specific fields
        await this.verifyMainContainsTexts(this.FRENCH_TEXTS.FACILITY.FACILITY_FIELDS);

        // Verify address fields
        await this.verifyMainContainsTexts([
            'Address Type',
            'Pays',
            'Address Line 1',
            'Address Line 2',
            'Ville',
            'Province/State',
            'Postal Code'
        ]);

        // Verify facility switches/toggles
        await this.verifyFacilitySwitches();
    }

    async verifyViewFacilityGroupPageButtons() {
        // Verify heading
        await expect(this.page.getByRole('heading')).toContainText(this.FRENCH_TEXTS.FACILITY.HEADINGS.VIEW_GROUP);

        // Verify buttons
        await expect(this.page.getByRole('button', { name: this.FRENCH_TEXTS.COMMON_BUTTONS.EDIT, exact: true })).toBeVisible();
        await this.verifyButtonArray(this.FRENCH_TEXTS.FACILITY.VIEW_GROUP_BUTTONS);

        // Verify all common fields including creation/modification dates
        await this.verifyMainContainsTexts([
            ...this.FRENCH_TEXTS.FACILITY.COMMON_FIELDS,
            ...this.FRENCH_TEXTS.FACILITY.GROUP_FIELDS
        ]);

        // Verify facility switches
        await this.verifyFacilitySwitches();
    }

    async verifyCreateFacilityGroupPageButtons() {
        // Verify heading
        await expect(this.page.getByRole('heading')).toContainText(this.FRENCH_TEXTS.FACILITY.HEADINGS.CREATE_GROUP);

        // Verify buttons using helper method
        await this.verifyFacilityGroupButtons([
            this.FRENCH_TEXTS.COMMON_BUTTONS.SUBMIT,
            ...this.FRENCH_TEXTS.FACILITY.GROUP_BUTTONS
        ]);

        // Verify common facility group elements
        await this.verifyFacilityGroupCommonElements();
    }

    async verifyEditFacilityGroupPageButtons() {
        // Verify heading
        await expect(this.page.getByRole('heading')).toContainText(this.FRENCH_TEXTS.FACILITY.HEADINGS.EDIT_GROUP);

        // Verify buttons using helper method
        await this.verifyFacilityGroupButtons([
            this.FRENCH_TEXTS.COMMON_BUTTONS.SAVE,
            ...this.FRENCH_TEXTS.FACILITY.EDIT_GROUP_BUTTONS
        ]);

        // Verify all common fields including creation/modification dates
        await this.verifyMainContainsTexts([
            ...this.FRENCH_TEXTS.FACILITY.COMMON_FIELDS,
            ...this.FRENCH_TEXTS.FACILITY.GROUP_FIELDS
        ]);

        // Verify facility switches
        await this.verifyFacilitySwitches();
    }


    async verifyFacilityFilterByButtonValidation() {
        // Verify filter fields using constants
        await this.verifyMainContainsTexts([
            ...this.FRENCH_TEXTS.FACILITY.FILTER_FIELDS,
            this.FRENCH_TEXTS.FACILITY.COMMON_FIELDS[1] // Marque
        ]);

        // Verify radio group options
        await expect(this.page.getByRole('radiogroup')).toContainText('Groupe d\'établissement');
        await expect(this.page.getByRole('radiogroup')).toContainText('Établissement');

        // Switch to facilities view
        await this.page.locator('#facilities').click();

        // Verify status checkboxes using constants
        for (const status of this.FRENCH_TEXTS.FACILITY.STATUS_OPTIONS) {
            const checkboxId = `#checkbox-group-statut-${status.toLowerCase()}-label`;
            await expect(this.page.locator(checkboxId)).toContainText(status);
        }

        // Verify action buttons
        await this.verifyButtonArray([
            this.FRENCH_TEXTS.COMMON_BUTTONS.SEARCH,
            'Réinitialiser',
            'Ajouter un nouvel é',
            'Ajouter un nouveau groupe d\'é'
        ]);

        // Verify filter message
        await expect(this.page.getByRole('main')).toContainText(this.FRENCH_TEXTS.FACILITY.MESSAGES.APPLY_FILTERS);

        // Test search functionality
        await this.testFacilitySearch();
    }

    private async testFacilitySearch(): Promise<void> {
        // Test facility search with no results
        await this.page.getByRole('textbox', { name: 'Établissement' }).fill('765456543');
        await this.page.getByRole('button', { name: this.FRENCH_TEXTS.COMMON_BUTTONS.SEARCH }).click();
        await expect(this.page.getByRole('heading')).toContainText('facilities');
        await expect(this.page.getByRole('paragraph')).toContainText(this.FRENCH_TEXTS.FACILITY.MESSAGES.NO_DATA);

        // Reset and search facility groups
        await this.page.getByRole('button', { name: this.FRENCH_TEXTS.COMMON_BUTTONS.FILTER_BY }).click();
        await this.page.getByRole('button', { name: 'Réinitialiser' }).click();
        await this.page.getByRole('button', { name: this.FRENCH_TEXTS.COMMON_BUTTONS.SEARCH }).click();
        await expect(this.page.getByRole('heading')).toContainText('facility groups');
    }





}