import { BeforeAll, AfterAll, Before, After, Status, setDefaultTimeout  } from "@cucumber/cucumber";
import { <PERSON><PERSON><PERSON>, <PERSON>rowserContext } from "@playwright/test";
import { fixture } from "./pageFixture";
import { invokeBrowser } from "../helper/browsers/browserManager";
import { getEnv } from "../helper/env/env";
import { createLogger } from "winston";
import { options } from "../helper/util/logger";

const fs = require("fs-extra");
// Configure timeout based on environment or debug mode
const getTimeout = () => {
    if (process.env.PWDEBUG === '1') {
        return 300 * 1000; // 5 minutes for debug mode
    }
    return parseInt(process.env.CUCUMBER_TIMEOUT || '180') * 1000; // Default 180s (3 minutes), configurable via env
};

// @ts-ignore
setDefaultTimeout(getTimeout());
let browser: Browser;
let context: BrowserContext;

BeforeAll(async function () {
    getEnv();
    browser = await invoke<PERSON>rowser();
});
// It will trigger for not auth scenarios
Before({ tags: "not @auth" }, async function ({ pickle }) {
    const scenarioName = pickle.name + pickle.id;
   
    if(/^true$/i.test(process.env.ENABLE_VIDEOS)){
        context = await browser.newContext({
            storageState: getStorageState(pickle.name),
            recordVideo: {
                dir: "test-results/videos",
            },
        });
    }else{
       context = await browser.newContext({
        storageState: getStorageState(pickle.name)
       }); 
    }
    if(/^true$/i.test(process.env.ENABLE_TRACING)){
        await context.tracing.start({
            name: scenarioName,
            title: pickle.name,
            sources: true,
            screenshots: true, snapshots: true
        });
    }
    const page = await context.newPage();
    fixture.page = page;
    fixture.logger = createLogger(options(scenarioName));
});

// Add auth hook for scenarios with @auth tag
Before({ tags: "@auth" }, async function ({ pickle }) {
    const scenarioName = pickle.name + pickle.id;
   
    // Only create new context if it doesn't exist or browser is disconnected
    if (!context || !context.browser().isConnected()) {
        if(/^true$/i.test(process.env.ENABLE_VIDEOS)){
            context = await browser.newContext({
                storageState: "src/helper/auth/supplier.json",
                recordVideo: {
                    dir: "test-results/videos",
                },
            });
        }else{
           context = await browser.newContext({
            storageState: "src/helper/auth/supplier.json"
           }); 
        }
    }
    
    if(/^true$/i.test(process.env.ENABLE_TRACING)){
        await context.tracing.start({
            name: scenarioName,
            title: pickle.name,
            sources: true,
            screenshots: true, snapshots: true
        });
    }
    
    const page = await context.newPage();
    fixture.page = page;
    fixture.logger = createLogger(options(scenarioName));
});


After(async function ({ pickle, result }) {
    let videoPath: string;
    let img: Buffer;
    const path = `./test-results/trace/${pickle.id}.zip`;
    if (result?.status == Status.PASSED) {

        img = await fixture.page.screenshot(
            { path: `./test-results/screenshots/${pickle.name}.png`, type: "png" })
        if(/^true$/i.test(process.env.ENABLE_VIDEOS)){    
            videoPath = await fixture.page.video().path();
        }
    }

    if (result.status !== Status.PASSED) {
		fixture.logger.error("Step Name: " + pickle.name);
		fixture.logger.error("Step failed with error: " + result.exception);
		fixture.logger.error("Message: " + result.exception.message + ", Error Type: " + result.exception.type);
		img = await fixture.page.screenshot(
            { path: `./test-results/screenshots/${pickle.name}.png`, type: "png" })
        await this.attach(
            img, "image/png"
        );    
	}
    if(/^true$/i.test(process.env.ENABLE_TRACING)){
        await context.tracing.stop({ path: path });
    }

        // Always close the page
    await fixture.page.close();
    
    // Only close context if it's the last scenario
    const isLastScenario = this.parameters?.isLast || false;
    if (isLastScenario) {
        await context.close();
    }
    // await fixture.page.close();
    // await context.close();
    if (result?.status == Status.PASSED) {
        await this.attach(
            img, "image/png"
        );
        if(/^true$/i.test(process.env.ENABLE_VIDEOS)){
            await this.attach(
                fs.readFileSync(videoPath),
                'video/webm'
            );
        }
        if(/^true$/i.test(process.env.ENABLE_TRACING)){
            const traceFileLink = `<a href="https://trace.playwright.dev/">Open ${path}</a>`
            await this.attach(`Trace file: ${traceFileLink}`, 'text/html');
        }

    }

});

AfterAll(async function () {
    await browser.close();
})

function getStorageState(user: string): string | { cookies: { name: string; value: string; domain: string; path: string; expires: number; httpOnly: boolean; secure: boolean; sameSite: "Strict" | "Lax" | "None"; }[]; origins: { origin: string; localStorage: { name: string; value: string; }[]; }[]; } {
    if(user.includes("Super Admin"))
        return "src/helper/auth/admin.json";
}


